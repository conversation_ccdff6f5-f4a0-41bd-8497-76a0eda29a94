<?php

use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;
use App\Livewire\AdminDashboard;
use App\Livewire\CompanyManager;
use App\Livewire\UserConfigManager;

Route::get('/', function () {
    return view('welcome');
});

Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // Admin routes
    Route::get('/admin', AdminDashboard::class)->name('admin.dashboard');
    Route::get('/admin/companies', CompanyManager::class)->name('admin.companies');
    Route::get('/admin/configs', UserConfigManager::class)->name('admin.configs');
});

require __DIR__.'/auth.php';
