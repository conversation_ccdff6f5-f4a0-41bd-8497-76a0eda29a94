<?php

use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;
use App\Livewire\AdminDashboard;
use App\Livewire\CompanyManager;
use App\Livewire\UserConfigManager;
use App\Livewire\Companies\CompaniesIndex;
use App\Livewire\Accounting\FinancialYears;

Route::get('/', function () {
    return redirect()->route('login');
});

Route::get('/dashboard', function () {
    return redirect()->route('admin.dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // Admin routes
    Route::get('/admin', AdminDashboard::class)->name('admin.dashboard');
    Route::get('/admin/test', function() { return view('test-admin'); })->name('admin.test');

    // Companies routes
    Route::get('/admin/companies', CompaniesIndex::class)->name('admin.companies');
    Route::get('/admin/companies/create', function() { return 'Create Company'; })->name('admin.companies.create');

    // Accounting routes
    Route::get('/admin/accounting/financial-years', FinancialYears::class)->name('admin.accounting.financial-years');

    // Chart of Accounts routes (placeholder for now)
    Route::get('/admin/chart-of-accounts', function() { return 'Chart of Accounts Index'; })->name('admin.chart-of-accounts');
    Route::get('/admin/chart-of-accounts/create', function() { return 'Create Account'; })->name('admin.chart-of-accounts.create');

    Route::get('/admin/configs', UserConfigManager::class)->name('admin.configs');
});

require __DIR__.'/auth.php';
